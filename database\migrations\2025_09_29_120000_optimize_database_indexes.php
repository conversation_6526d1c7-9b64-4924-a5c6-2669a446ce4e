<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Optimize product_categories table
        Schema::table('product_categories', function (Blueprint $table) {
            // Add indexes for commonly queried columns
            $table->index('status', 'idx_product_categories_status');
            $table->index('created_at', 'idx_product_categories_created_at');
            $table->index(['status', 'created_at'], 'idx_product_categories_status_created');
            $table->index('deleted_at', 'idx_product_categories_deleted_at');
            
            // Add full-text index for category search
            $table->fullText('category', 'ft_product_categories_category');
        });

        // Optimize users table
        Schema::table('users', function (Blueprint $table) {
            // Add indexes for authentication and common queries
            $table->index('email', 'idx_users_email');
            $table->index('email_verified_at', 'idx_users_email_verified');
            $table->index('created_at', 'idx_users_created_at');
            $table->index('updated_at', 'idx_users_updated_at');
        });

        // Create admin activity log table for performance monitoring
        Schema::create('admin_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('action', 100);
            $table->string('model_type', 100)->nullable();
            $table->unsignedBigInteger('model_id')->nullable();
            $table->json('changes')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamp('created_at')->useCurrent();
            
            // Indexes for performance
            $table->index('user_id', 'idx_activity_logs_user_id');
            $table->index('action', 'idx_activity_logs_action');
            $table->index('created_at', 'idx_activity_logs_created_at');
            $table->index(['model_type', 'model_id'], 'idx_activity_logs_model');
            $table->index('ip_address', 'idx_activity_logs_ip');
            
            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // Create query cache table for better performance
        Schema::create('query_cache', function (Blueprint $table) {
            $table->string('key', 255)->primary();
            $table->longText('value');
            $table->integer('expiration');
            
            // Index for expiration cleanup
            $table->index('expiration', 'idx_query_cache_expiration');
        });

        // Create database performance metrics table
        Schema::create('performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('metric_name', 100);
            $table->string('metric_type', 50); // query_time, memory_usage, etc.
            $table->decimal('value', 10, 4);
            $table->string('route', 255)->nullable();
            $table->string('method', 10)->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('recorded_at')->useCurrent();
            
            // Indexes for analytics
            $table->index('metric_name', 'idx_metrics_name');
            $table->index('metric_type', 'idx_metrics_type');
            $table->index('recorded_at', 'idx_metrics_recorded_at');
            $table->index(['metric_name', 'recorded_at'], 'idx_metrics_name_time');
            $table->index('route', 'idx_metrics_route');
        });

        // Optimize existing cache table if it exists
        if (Schema::hasTable('cache')) {
            Schema::table('cache', function (Blueprint $table) {
                if (!Schema::hasIndex('cache', 'idx_cache_expiration')) {
                    $table->index('expiration', 'idx_cache_expiration');
                }
            });
        }

        // Add database-level optimizations
        $this->addDatabaseOptimizations();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop performance-related tables
        Schema::dropIfExists('performance_metrics');
        Schema::dropIfExists('query_cache');
        Schema::dropIfExists('admin_activity_logs');

        // Remove indexes from product_categories
        Schema::table('product_categories', function (Blueprint $table) {
            $table->dropIndex('idx_product_categories_status');
            $table->dropIndex('idx_product_categories_created_at');
            $table->dropIndex('idx_product_categories_status_created');
            $table->dropIndex('idx_product_categories_deleted_at');
            $table->dropFullText('ft_product_categories_category');
        });

        // Remove indexes from users
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_email');
            $table->dropIndex('idx_users_email_verified');
            $table->dropIndex('idx_users_created_at');
            $table->dropIndex('idx_users_updated_at');
        });
    }

    /**
     * Add database-level optimizations
     */
    private function addDatabaseOptimizations(): void
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}");

        if ($connection['driver'] === 'mysql') {
            // MySQL-specific optimizations
            DB::statement('SET SESSION query_cache_type = ON');
            DB::statement('SET SESSION query_cache_size = 67108864'); // 64MB
            
            // Optimize tables
            $tables = ['users', 'product_categories', 'cache'];
            foreach ($tables as $table) {
                if (Schema::hasTable($table)) {
                    DB::statement("OPTIMIZE TABLE {$table}");
                }
            }
        }

        if ($connection['driver'] === 'pgsql') {
            // PostgreSQL-specific optimizations
            DB::statement('VACUUM ANALYZE');
        }
    }
}
