<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProductCategory;
use App\Helpers\Helper;
use App\Helpers\DatabaseHelper;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\Cache;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (request()->ajax()) {
            return DatabaseHelper::monitoredQuery(function () {
                // Optimize query with specific columns and indexes
                $query = ProductCategory::select(['id', 'category', 'status', 'created_at'])
                    ->orderBy('created_at', 'desc');

                // Apply DataTables optimizations
                $query = DatabaseHelper::optimizeDataTablesQuery(
                    $query,
                    ['id', 'category', 'status', 'created_at'],
                    ['category']
                );

                return DataTables::of($query)
                    ->addIndexColumn()
                    ->addColumn('status_badge', function($row) {
                        return Helper::getStatusBadge($row->status === 'active' ? 1 : 0);
                    })
                    ->addColumn('actions', function($row) {
                        return Helper::getActionButtons($row->id, 'product-categories', ['edit', 'delete']);
                    })
                    ->editColumn('created_at', function($row) {
                        return $row->created_at->format('M d, Y H:i');
                    })
                    ->filterColumn('category', function($query, $keyword) {
                        $query->where('category', 'LIKE', "%{$keyword}%");
                    })
                    ->rawColumns(['status_badge', 'actions'])
                    ->make(true);
            }, 'product_categories_datatable');
        }

        // Cache the view data for better performance
        $viewData = Cache::remember('product_categories_view_data', 300, function () {
            return [
                'total_categories' => ProductCategory::count(),
                'active_categories' => ProductCategory::where('status', 'active')->count(),
                'inactive_categories' => ProductCategory::where('status', 'inactive')->count(),
            ];
        });

        return view('ProductCategory.index', $viewData);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return DatabaseHelper::monitoredQuery(function () use ($request) {
            $validated = $request->validate([
                'category' => 'required|string|max:255|unique:product_categories,category,' . $request->id,
                'status' => 'required|string|in:active,inactive',
            ]);

            $cat = ProductCategory::updateOrCreate(
                ['id' => $request->id],
                $validated
            );

            // Clear related cache
            Cache::forget('product_categories_view_data');
            Cache::tags(['product_categories'])->flush();

            return Helper::successResponse('Category saved successfully', $cat);
        }, 'product_category_store');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($id) {
            // Use cache for frequently accessed categories
            $cat = Cache::remember("product_category_{$id}", 300, function () use ($id) {
                return ProductCategory::findOrFail($id);
            });

            return Helper::successResponse('Category fetched', $cat);
        }, 'product_category_edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($request, $id) {
            $validated = $request->validate([
                'category' => 'required|string|max:255|unique:product_categories,category,' . $id,
                'status' => 'required|string|in:active,inactive',
            ]);

            $cat = ProductCategory::findOrFail($id);
            $cat->update($validated);

            // Clear related cache
            Cache::forget("product_category_{$id}");
            Cache::forget('product_categories_view_data');
            Cache::tags(['product_categories'])->flush();

            return Helper::successResponse('Category updated', $cat);
        }, 'product_category_update');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return DatabaseHelper::monitoredQuery(function () use ($id) {
            $cat = ProductCategory::findOrFail($id);
            $cat->delete();

            // Clear related cache
            Cache::forget("product_category_{$id}");
            Cache::forget('product_categories_view_data');
            Cache::tags(['product_categories'])->flush();

            return Helper::successResponse('Category deleted');
        }, 'product_category_destroy');
    }
}
