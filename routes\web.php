<?php

use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\ProductCategoryController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductAttributeController;
use App\Http\Controllers\ProductVariantController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\StockTransferController;
use App\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

// Privacy Policy Routes
Route::get('/privacy-policy', function () {
    return view('Rules.privacy-policy');
})->name('privacy-policy');

// Support Routes
Route::get('/support', function () {
    return view('Rules.support');
})->name('support');

Route::group(['middleware' => ['admin.auth']], function () {
    // Page Under Construction Routes
    Route::get('/page-under-construction', function () {
        return view('Layouts.message');
    })->name('page-under-construction');

    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // Product Category CRUD (AJAX)
    Route::resource('product-categories', ProductCategoryController::class);

    // Product Management Routes
    Route::resource('products', ProductController::class);
    Route::get('products/{product}/variants', [ProductController::class, 'variants'])->name('products.variants');
    Route::get('products/{product}/inventory', [ProductController::class, 'inventory'])->name('products.inventory');

    // Product Attribute Management Routes
    Route::resource('product-attributes', ProductAttributeController::class);
    Route::get('product-attributes/{attribute}/values', [ProductAttributeController::class, 'values'])->name('product-attributes.values');

    // Product Variant Management Routes
    Route::resource('product-variants', ProductVariantController::class);
    Route::post('product-variants/{variant}/update-stock', [ProductVariantController::class, 'updateStock'])->name('product-variants.update-stock');

    // Brand Management Routes
    Route::resource('brands', BrandController::class);

    // Inventory Management Routes
    Route::resource('inventory', InventoryController::class)->only(['index', 'show']);
    Route::post('inventory/adjust', [InventoryController::class, 'adjust'])->name('inventory.adjust');
    Route::get('inventory/low-stock', [InventoryController::class, 'lowStock'])->name('inventory.low-stock');
    Route::get('inventory/out-of-stock', [InventoryController::class, 'outOfStock'])->name('inventory.out-of-stock');
    Route::get('inventory/reports', [InventoryController::class, 'reports'])->name('inventory.reports');

    // Warehouse Management Routes
    Route::resource('warehouses', WarehouseController::class);
    Route::get('warehouses/{warehouse}/inventory', [WarehouseController::class, 'inventory'])->name('warehouses.inventory');

    // Stock Transfer Routes
    Route::resource('stock-transfers', StockTransferController::class);
    Route::post('stock-transfers/{transfer}/approve', [StockTransferController::class, 'approve'])->name('stock-transfers.approve');
    Route::post('stock-transfers/{transfer}/reject', [StockTransferController::class, 'reject'])->name('stock-transfers.reject');
    Route::post('stock-transfers/{transfer}/complete', [StockTransferController::class, 'complete'])->name('stock-transfers.complete');

    // Supplier Management Routes
    Route::resource('suppliers', SupplierController::class);
    Route::get('suppliers/{supplier}/products', [SupplierController::class, 'products'])->name('suppliers.products');
    Route::post('suppliers/{supplier}/add-product', [SupplierController::class, 'addProduct'])->name('suppliers.add-product');
    Route::delete('suppliers/{supplier}/remove-product/{product}', [SupplierController::class, 'removeProduct'])->name('suppliers.remove-product');
});
